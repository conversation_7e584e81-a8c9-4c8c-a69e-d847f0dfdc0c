/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: url('background.jpg') center/cover no-repeat;
    height: 100vh;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* Container para gif1 */
.gif1-container {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif1 {
    position: absolute;
    width: 300px; /* Aumentando 1,5x (200px * 1,5 = 300px) */
    height: auto;
    animation: moveLeftToRight 50s infinite linear; /* Novo ciclo total: 50s */
}

/* Container para gif2 e gif3 */
.gif2-gif3-container {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif2, .gif3 {
    position: absolute;
    width: 150px; /* Mesmo tamanho para ambos */
    height: auto;
    animation: moveRightToLeft 50s infinite linear; /* Novo ciclo total: 50s */
}

.gif2 {
    top: 0; /* Mesma altura - centro */
    animation-delay: 15s; /* Gif1 (10s) + intervalo (5s) = 15s */
}

.gif3 {
    top: 0; /* Mesma altura - centro */
    animation: moveRightToLeftGif3 50s infinite linear; /* Animação específica para gif3 */
}

/* Animação: gif1 da esquerda para direita - 10s movimento em ciclo de 50s */
@keyframes moveLeftToRight {
    0% {
        transform: translateX(-350px) scaleX(-1); /* Começa fora da tela à esquerda + flip */
        opacity: 1;
    }
    20% { /* 10s de 50s = 20% */
        transform: translateX(calc(100vw + 350px)) scaleX(-1); /* Sai completamente da tela à direita + flip */
        opacity: 1;
    }
    20.01% {
        opacity: 0; /* Fica invisível durante a pausa */
    }
    100% {
        transform: translateX(-350px) scaleX(-1); /* Retorna à posição inicial + flip */
        opacity: 0;
    }
}

/* Animação: gif2 e gif3 da direita para esquerda - 14s movimento em ciclo de 50s */
@keyframes moveRightToLeft {
    0% {
        transform: translateX(calc(100vw + 200px)); /* Começa fora da tela à direita */
        opacity: 0;
    }
    29.99% { /* Espera até 30% (15s de 50s) */
        transform: translateX(calc(100vw + 200px));
        opacity: 0;
    }
    30% { /* Começa a se mover aos 15s */
        transform: translateX(calc(100vw + 200px));
        opacity: 1;
    }
    58% { /* Termina aos 29s (14s de movimento) */
        transform: translateX(-200px); /* Sai completamente da tela à esquerda */
        opacity: 1;
    }
    58.01% {
        opacity: 0; /* Fica invisível após sair */
    }
    100% {
        transform: translateX(calc(100vw + 200px)); /* Retorna à posição inicial */
        opacity: 0;
    }
}

/* Animação específica para gif3 - começa 2s após gif2 (aos 31s) */
@keyframes moveRightToLeftGif3 {
    0% {
        transform: translateX(calc(100vw + 200px)); /* Começa fora da tela à direita */
        opacity: 0;
    }
    61.99% { /* Espera até 62% (31s de 50s) */
        transform: translateX(calc(100vw + 200px));
        opacity: 0;
    }
    62% { /* Começa a se mover aos 31s */
        transform: translateX(calc(100vw + 200px));
        opacity: 1;
    }
    90% { /* Termina aos 45s (14s de movimento) */
        transform: translateX(-200px); /* Sai completamente da tela à esquerda */
        opacity: 1;
    }
    90.01% {
        opacity: 0; /* Fica invisível após sair */
    }
    100% {
        transform: translateX(calc(100vw + 200px)); /* Retorna à posição inicial */
        opacity: 0;
    }
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .gif1 {
        width: 200px; /* Mantém proporção menor em telas pequenas */
    }

    .gif2, .gif3 {
        width: 120px; /* Mantém proporção menor em telas pequenas */
    }
}