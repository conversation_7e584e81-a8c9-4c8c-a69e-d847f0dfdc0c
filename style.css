/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: url('background.jpg') center/cover no-repeat;
    height: 100vh;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* Container para gif1 */
.gif1-container {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif1 {
    position: absolute;
    width: 200px; /* Aumentando o tamanho da imagem */
    height: auto;
    animation: moveLeftToRight 25s infinite linear; /* 10s movimento + 15s pausa */
}

/* Container para gif2 e gif3 */
.gif2-gif3-container {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif2, .gif3 {
    position: absolute;
    width: 150px; /* Mesmo tamanho para ambos */
    height: auto;
    animation: moveRightToLeft 25s infinite linear; /* 10s movimento + 15s pausa */
}

.gif2 {
    top: 0; /* Mesma altura - centro */
    animation-delay: 15s; /* Gif1 (10s) + intervalo (5s) */
}

.gif3 {
    top: 0; /* Mesma altura - centro */
    animation-delay: 35s; /* Gif1 (10s) + intervalo (5s) + gif2 (10s) + pausa (5s) + gif3 (10s) */
}

/* Animação: gif1 da esquerda para direita - 10s movimento + 15s pausa */
@keyframes moveLeftToRight {
    0% {
        transform: translateX(-250px); /* Começa fora da tela à esquerda */
        opacity: 1;
    }
    40% { /* 10s de 25s = 40% */
        transform: translateX(calc(100vw + 250px)); /* Sai completamente da tela à direita */
        opacity: 1;
    }
    40.01% {
        opacity: 0; /* Fica invisível durante a pausa */
    }
    100% {
        transform: translateX(-250px); /* Retorna à posição inicial */
        opacity: 0;
    }
}

/* Animação: gif2 e gif3 da direita para esquerda - 10s movimento + 15s pausa */
@keyframes moveRightToLeft {
    0% {
        transform: translateX(calc(100vw + 200px)); /* Começa fora da tela à direita */
        opacity: 0;
    }
    59.99% { /* Espera até 60% (15s de 25s) */
        transform: translateX(calc(100vw + 200px));
        opacity: 0;
    }
    60% { /* Começa a se mover aos 15s */
        transform: translateX(calc(100vw + 200px));
        opacity: 1;
    }
    100% { /* Termina aos 25s (10s de movimento) */
        transform: translateX(-200px); /* Sai completamente da tela à esquerda */
        opacity: 1;
    }
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .gif1, .gif2, .gif3 {
        width: 100px;
    }

    .gif2 {
        top: -40px;
    }

    .gif3 {
        top: 40px;
    }
}