/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: url('background.jpg') center/cover no-repeat;
    height: 100vh;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* Container para gif1 */
.gif1-container {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif1 {
    position: absolute;
    width: 150px;
    height: auto;
    animation: moveLeftToRight 6s infinite;
}

/* Container para gif2 e gif3 */
.gif2-gif3-container {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.gif2, .gif3 {
    position: absolute;
    width: 120px;
    height: auto;
    animation: moveRightToLeft 6s infinite;
    animation-delay: 3s; /* Começa depois que gif1 sai da tela */
}

.gif2 {
    top: -60px; /* Posiciona gif2 acima do centro */
}

.gif3 {
    top: 60px; /* Posiciona gif3 abaixo do centro */
}

/* Animação: gif1 da esquerda para direita */
@keyframes moveLeftToRight {
    0% {
        transform: translateX(-200px); /* Começa fora da tela à esquerda */
        opacity: 1;
    }
    50% {
        transform: translateX(calc(100vw + 200px)); /* Sai completamente da tela à direita */
        opacity: 1;
    }
    50.01% {
        opacity: 0; /* Fica invisível durante a segunda metade */
    }
    100% {
        transform: translateX(-200px); /* Retorna à posição inicial */
        opacity: 0;
    }
}

/* Animação: gif2 e gif3 da direita para esquerda */
@keyframes moveRightToLeft {
    0% {
        transform: translateX(calc(100vw + 200px)); /* Começa fora da tela à direita */
        opacity: 0;
    }
    49.99% {
        opacity: 0; /* Permanece invisível durante a primeira metade */
    }
    50% {
        transform: translateX(calc(100vw + 200px));
        opacity: 1; /* Torna-se visível */
    }
    100% {
        transform: translateX(-200px); /* Sai completamente da tela à esquerda */
        opacity: 1;
    }
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .gif1, .gif2, .gif3 {
        width: 100px;
    }

    .gif2 {
        top: -40px;
    }

    .gif3 {
        top: 40px;
    }
}